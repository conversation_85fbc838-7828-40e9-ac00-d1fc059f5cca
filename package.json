{"name": "kashf", "version": "1.0.0", "description": "kashf backend", "license": "MIT", "engines": {"node": ">=18.0.0"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.build.json && copyfiles ormconfig.js ./dist/", "format": "prettier --write \"src/**/*.ts\"", "start": "node dist/main.js", "start:dev": "cross-env NODE_ENV=development nodemon --exec \"ts-node -r tsconfig-paths/register src/main.ts\" --watch src --ext ts", "start:dev:nodemon": "cross-env NODE_ENV=development nodemon --exec \"node -r ts-node/register src/main.ts\"", "start:dev:build": "cross-env NODE_ENV=development nodemon --watch src --ext ts --exec \"npm run build && npx dotenv -e env/development.env -- node dist/main.js\"", "start:debug": "cross-env NODE_ENV=development nest start --debug 0.0.0.0:9229 --watch", "start:prod": "cross-env NODE_ENV=production node dist/main.js", "lint": "eslint \"src/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "typeorm:create": "npm run typeorm migration:create -- -n", "typeorm:run": "cross-env NODE_ENV=development node -r ts-node/register ./node_modules/typeorm/cli.js migration:run -d ./ormconfig.js", "typeorm:generate": "cross-env NODE_ENV=development node -r ts-node/register ./node_modules/typeorm/cli.js migration:generate -d ./ormconfig.js -n", "typeorm:revert": "cross-env NODE_ENV=development node -r ts-node/register ./node_modules/typeorm/cli.js migration:revert -d ./ormconfig.js", "repl": "npm run build && node dist/repl", "repl:dev": "cross-env NODE_ENV=development ts-node -r tsconfig-paths/register src/repl.ts", "repl:gui": "cross-env NODE_ENV=development ts-node -r tsconfig-paths/register src/repl.ts"}, "dependencies": {"@nestjs/axios": "1.0.1", "@nestjs/common": "^9.4.3", "@nestjs/core": "^9.4.3", "@nestjs/jwt": "^10.1.0", "@nestjs/microservices": "^9.4.3", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/platform-socket.io": "^9.4.3", "@nestjs/schedule": "^2.2.3", "@nestjs/serve-static": "^3.0.1", "@nestjs/swagger": "^6.3.0", "@nestjs/typeorm": "^9.0.1", "@nestjs/websockets": "^9.4.3", "@types/socket.io": "^3.0.2", "agora-access-token": "^2.0.4", "aws-sdk": "^2.812.0", "axios": "^1.4.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "dotenv-cli": "^7.2.1", "express": "^4.18.2", "express-rate-limit": "^6.7.1", "express-static-gzip": "^3.0.0", "file-type": "^15.0.0", "firebase-admin": "^9.4.1", "hbs": "^4.1.1", "helmet": "^7.0.0", "joi": "^17.2.1", "json2csv": "^5.0.6", "jsonwebtoken": "^8.5.1", "jspdf": "^2.5.1", "lodash": "^4.17.20", "mime-types": "^2.1.27", "moment": "^2.29.1", "morgan": "^1.10.0", "multer": "^1.4.0", "nestjs-i18n": "^8.0.8", "nodemailer": "^6.4.17", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "pdfkit": "^0.15.0", "pg": "^8.3.3", "reflect-metadata": "^0.1.13", "request-context": "^2.0.0", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "source-map-support": "^0.5.19", "swagger-ui-express": "^4.1.4", "twilio": "^3.55.0", "typeorm": "^0.3.17", "typeorm-extension": "^2.8.1", "typeorm-naming-strategies": "^4.1.0", "typeorm-transactional-cls-hooked": "^0.1.21", "typescript": "^4.9.5", "uuid": "^8.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/testing": "^9.4.3", "@types/aws-sdk": "^2.7.0", "@types/axios": "^0.14.0", "@types/bcrypt": "^5.0.0", "@types/compression": "^1.7.0", "@types/express": "^4.17.17", "@types/express-rate-limit": "^6.0.0", "@types/file-type": "^10.9.1", "@types/hbs": "^4.0.1", "@types/jest": "^29.5.2", "@types/json2csv": "^5.0.1", "@types/jsonwebtoken": "^8.5.0", "@types/lodash": "^4.14.161", "@types/mime-types": "^2.1.0", "@types/moment": "^2.13.0", "@types/morgan": "^1.9.1", "@types/node": "^18.16.19", "@types/nodemailer": "^6.4.0", "@types/passport-jwt": "^3.0.3", "@types/superagent": "^4.1.15", "@types/supertest": "^2.0.12", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "copyfiles": "^2.4.1", "cross-env": "^7.0.2", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-import-helpers": "^1.3.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^10.0.0", "gh-pages": "^2.1.1", "husky": "^3.0.9", "jest": "^29.5.0", "lint-staged": "^13.2.3", "nodemon": "^2.0.22", "prettier": "^3.0.0", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1"}, "resolutions": {"@types/superagent": "4.1.15"}, "lint-staged": {"*.ts": ["eslint --fix", "git add"]}}