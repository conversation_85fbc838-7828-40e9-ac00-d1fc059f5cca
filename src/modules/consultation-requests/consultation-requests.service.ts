/* eslint-disable max-len */
/* eslint-disable complexity */
/* eslint-disable simple-import-sort/sort */
import {
    BadRequestException,
    forwardRef,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import * as moment from 'moment';
import { I18nService } from 'nestjs-i18n';

import { DataSource, In, MoreThanOrEqual, SelectQueryBuilder } from 'typeorm';

import {
    ConsultationRequestStatus,
    PaymentStatus,
    ReminderType,
} from '../../common/constants/status';
import {
    CancelledByType,
    ConsultationType,
    DEACTIVATION_REASON,
    FilterByType,
    FilterType,
    PaymentMethod,
    RoleType,
    SortByType,
    TransactionKind,
} from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { AgoraService } from '../../shared/services/agora.generate.access.token';
import { DataDownloadService } from '../../shared/services/data-download.service';
import { GeneratorService } from '../../shared/services/generator.service';
import { HelperService } from '../../shared/services/helper';
import { AuthMessagesKeys } from '../auth/translate.enum';
import { ClinicsService } from '../clinics/clinics.service';
import { Clinic } from '../clinics/entities/clinic.entity';
import { ClinicMessagesKeys } from '../clinics/translate.enum';
import { DoctorsMapper } from '../doctors/doctors.mapper';
import { DoctorsService } from '../doctors/doctors.service';
import { DoctorDto } from '../doctors/dto/doctor.dto';
import { DoctorEntity } from '../doctors/entities/doctor.entity';
import { CreateNotificationDto } from '../notifications/dto/create-notification.dto';
import { NotificationsService } from '../notifications/notifications.service';
import { Patient } from '../patients/entities/patient.entity';
import { PatientsService } from '../patients/patients.service';
import { TransactionStatus } from '../payments/payment.enum';
import { PaymentsService } from '../payments/services/payments.service';
import { TransactionsService } from '../payments/services/transactions.service';
import { RatingsService } from '../ratings/ratings.service';
import { ServiceProvider } from '../service-providers/entities/service-provider.entity';
import { ServiceProvidersRepository } from '../service-providers/repositories/service-providers.repository';
import { ServiceProviderMessagesKeys } from '../service-providers/translate.enum';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { ConsultationRequestsMapper } from './consultation-requests.mapper';
import { IFilter } from './consultation-requests.model';
import { ConsultationRequestDto } from './dto/consultation-request.dto';
import { ConsultationRequestsPageDto } from './dto/consultation-requests-page.dto';
import { CreateConsultationRequestDto } from './dto/create-consultation-request.dto';
import { DownloadConsultationRequestDto } from './dto/download-consultation-request.dto';
import { UpdateConsultationRequestDto } from './dto/update-consultation-request.dto';
import { ConsultationRequestEntity } from './entities/consultation-request.entity';
import {
    ConsultationRequestMessagesKeys,
    NotificationTyps,
} from './notification.enum';
import { ConsultationRequestsRepository } from './repositories/consultation-requests.repository';
import { EmailService } from '../../shared/services/email.service';
import { SimpleEmailBuilder } from '../../shared/utils/simple-email-builder';
import { DebugLogger } from '../../shared/services/logger.service';
import { ReminderService } from '../reminders/reminder.service';
import { S3Service } from '../cloud-providers/aws/s3/s3.service';
import * as fs from 'fs';

interface IValidationResult {
    doctor: DoctorEntity;
    patient: Patient;
    serviceProvider: ServiceProvider | null;
}

@Injectable()
export class ConsultationRequestsService {
    constructor(
        private readonly consultationRequestsMapper: ConsultationRequestsMapper,
        private readonly consultationRequestsRepository: ConsultationRequestsRepository,
        private readonly i18n: I18nService,
        private readonly agoraService: AgoraService,
        private readonly helperService: HelperService,
        @Inject(forwardRef(() => DoctorsService))
        private readonly doctorsService: DoctorsService,
        private readonly patientsService: PatientsService,
        private readonly doctorsMapper: DoctorsMapper,
        private readonly ratingService: RatingsService,
        private readonly notificationsService: NotificationsService,
        @Inject(forwardRef(() => PaymentsService))
        private readonly paymentService: PaymentsService,
        private generatorService: GeneratorService,
        private readonly clinicsService: ClinicsService,
        private readonly serviceProvidersRepository: ServiceProvidersRepository,
        @Inject(forwardRef(() => TransactionsService))
        private readonly transactionsService: TransactionsService,
        private readonly dataDownloadService: DataDownloadService,
        @Inject(forwardRef(() => EmailService))
        private readonly emailService: EmailService,
        private readonly logger: DebugLogger,
        @Inject(forwardRef(() => ReminderService))
        private readonly reminderService: ReminderService,
        @Inject(forwardRef(() => S3Service))
        private readonly s3Service: S3Service,
        private readonly dataSource: DataSource,
    ) {}

    private async findServiceProvider(id: number): Promise<ServiceProvider | null> {
        return this.serviceProvidersRepository.findOne({
            where: { id },
            relations: ['user'],
        });
    }

    async validateRequest(
        consultationRequestDto: CreateConsultationRequestDto,
        lang: string,
    ): Promise<IValidationResult> {
        // validate patient and doctor
        const [doctor, patient, serviceProvider] = await Promise.all([
            this.doctorsService.findDoctor(consultationRequestDto.doctorId),
            this.patientsService.findPatient(consultationRequestDto.patientId),
            consultationRequestDto.serviceProviderId
                ? this.findServiceProvider(consultationRequestDto.serviceProviderId)
                : null,
        ]);

        this.logger.log(consultationRequestDto.serviceProviderId);
        this.logger.log(serviceProvider);
        if (!doctor || !patient || !patient.isActive) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        if (consultationRequestDto.serviceProviderId && !serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        // validate clinic
        if (consultationRequestDto.type === ConsultationType.CLINIC) {
            const clinic = await this.clinicsService.findClinic(
                consultationRequestDto.clinicId,
            );
            if (!clinic) {
                throw new HttpException(
                    {
                        message: await this.i18n.translate(
                            ClinicMessagesKeys.CLINIC_NOT_FOUND,
                            {
                                lang,
                            },
                        ),
                    },
                    HttpStatus.BAD_REQUEST,
                );
            }
            if (clinic.isFreeClinic || consultationRequestDto.fees === 0) {
                consultationRequestDto.fees = 0;
                consultationRequestDto.isFreeRequest = true;
            }
        }
        if (
            ((doctor.isFreeCall || consultationRequestDto.fees === 0) &&
                consultationRequestDto.type ===
                    ConsultationType.ONLINE_CONSULTATION) ||
            ((doctor.isFreeVisit || consultationRequestDto.fees === 0) &&
                consultationRequestDto.type === ConsultationType.HOME_VISIT)
        ) {
            consultationRequestDto.fees = 0;
            consultationRequestDto.isFreeRequest = true;
        }

        if (
            serviceProvider &&
            (serviceProvider.isFreeCall || consultationRequestDto.fees === 0) &&
            consultationRequestDto.type === ConsultationType.ONLINE_CONSULTATION
        ) {
            consultationRequestDto.fees = 0;
            consultationRequestDto.isFreeRequest = true;
        }
        return { doctor, patient, serviceProvider };
    }

    async createRequest(
        createConsultationRequestDto: CreateConsultationRequestDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const { doctor, serviceProvider } = await this.validateRequest(
            createConsultationRequestDto,
            lang,
        );
        const consultationEntity =
            this.consultationRequestsMapper.fromDTOToEntity(
                ConsultationRequestEntity,
                createConsultationRequestDto,
            );
        consultationEntity.doctor = new DoctorEntity();
        consultationEntity.doctor.id = doctor.id;
        consultationEntity.kashfPercentage = doctor.kashfPercentage;
        consultationEntity.patient = new Patient();
        consultationEntity.patient.id = createConsultationRequestDto.patientId;

        if (createConsultationRequestDto.serviceProviderId) {
            consultationEntity.serviceProvider = new ServiceProvider();
            consultationEntity.serviceProvider.id = serviceProvider.id;
            consultationEntity.kashfPercentage =
                serviceProvider.kashfPercentage;
        }

        if (
            createConsultationRequestDto.type === ConsultationType.CLINIC &&
            createConsultationRequestDto.clinicId
        ) {
            consultationEntity.clinic = new Clinic();
            consultationEntity.clinic.id =
                createConsultationRequestDto.clinicId;
            delete consultationEntity.patientLocation;
            consultationEntity.code = this.generatorService
                .getCustomLengthRandomNumber(4)
                .toString();
        }

        if (
            createConsultationRequestDto.type ===
            ConsultationType.ONLINE_CONSULTATION
        ) {
            delete consultationEntity.patientLocation;
        }

        if (
            createConsultationRequestDto.type === ConsultationType.HOME_VISIT &&
            createConsultationRequestDto.patientLocation
        ) {
            const { latitude, longitude } =
                createConsultationRequestDto.patientLocation;

            consultationEntity.patientLocation = {
                type: 'Point',
                coordinates: [longitude, latitude],
            };
            consultationEntity.code = this.generatorService
                .getCustomLengthRandomNumber(4)
                .toString();
        }

        if (createConsultationRequestDto.from) {
            consultationEntity.from = moment(
                createConsultationRequestDto.from,
                'hh:mm A',
            ).toDate();
        }

        consultationEntity.date = moment(
            createConsultationRequestDto.date,
            'YYYY-MM-DD',
        ).toDate();

        const count = await this.checkLastExpiredRequests(doctor);
        if (count >= 3) {
            await this.doctorsService.setDoctorAsOffline(doctor.id);
        }

        const consultationRequest =
            this.consultationRequestsRepository.create(consultationEntity);

        const createdRequest =
            await this.consultationRequestsRepository.save(consultationRequest);

        const notification: CreateNotificationDto = {
            userId: doctor.user.id,
            data: {
                requestId: createdRequest.id.toString(),
                serviceProviderId: serviceProvider
                    ? serviceProvider.id.toString()
                    : String(null),
                type: NotificationTyps.REQUEST_CREATED,
            },
            notificationTranslations: [
                {
                    title: 'طلب جديد',
                    body: 'طلب جديد',
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'New Request',
                    body: 'New Request',
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };

        const createdNotification =
            await this.notificationsService.createNotification(notification);

        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                doctor.user.id,
                doctor.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ConsultationRequestMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
            createdId: createdRequest.id,
        };
    }

    private async checkLastExpiredRequests(doctor: DoctorEntity): Promise<number> {
        const result = await this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM consultation_requests
        WHERE id in
            (SELECT id FROM consultation_requests WHERE doctor_id = ${doctor.id}
            ORDER BY created_at DESC
            LIMIT 3)
          AND expired_at IS NOT null
          AND expired_at
            > (SELECT offline_at from doctors where id = ${doctor.id})
    `);

        return result[0].count || 0;
    }

    async updateRequest(
        consultationRequestDto: UpdateConsultationRequestDto,
        id: number,
        user: UserDetailsDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        this.helperService.removeEmptyKeys(consultationRequestDto);
        const request = await this.consultationRequestsRepository.findOne({
            where: { id },
            relations: [
                'patient',
                'doctor',
                'serviceProvider',
                'doctor.user',
                'patient.user',
                'transaction',
                'transaction.paymobTransactions',
                'reminders',
            ],
        });
        if (!request) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.REQUEST_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        const { date, from, status, isFeeCollected } = consultationRequestDto;
        if (date || from) {
            await this.sendTimeChangedNotification(request, date, from);
        }

        if (status) {
            const isStatusChanged = await this.checkifUserCanUpdateStatus(
                user,
                status,
                request,
                lang,
            );
            if (!isStatusChanged) {
                return {
                    isSuccessful: true,
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.UPDATED_SUCCESSFULLY,
                        {
                            lang,
                        },
                    ),
                };
            }

            switch (status) {
                case ConsultationRequestStatus.ACCEPTED:
                    consultationRequestDto.acceptedAt = moment()
                        .add(2, 'hour')
                        .toDate();
                    if (
                        request.type === ConsultationType.HOME_VISIT ||
                        request.type === ConsultationType.CLINIC
                    ) {
                        await this.notifyUserWithCode(request);
                    }
                    if (request.isFreeRequest) {
                        await this.sendAcceptNotification(request, user);
                    } else {
                        await this.sendProcedToPayNotification(request, user);
                    }

                    if (
                        request.doctor.canForwardToSecretary &&
                        !!request.doctor.secretaryEmail
                    ) {
                        await this.sendToSecretaryEmail(request);
                    }

                    await this.reminderService.createReminders(request);
                    break;
                case ConsultationRequestStatus.STARTED:
                    consultationRequestDto.startedAt = moment()
                        .add(2, 'hour')
                        .toDate();
                    await this.sendStartNotification(request, user);
                    await this.doctorsService.updateDoctorInaCall(
                        request.doctor.id,
                        true,
                    );
                    break;
                // TODO: WE NEED TO ADD DONATION LOGIC HERE IF THERE IS AMOUNT SENT WITH THE REQUEST
                case ConsultationRequestStatus.FINISHED:
                    if (
                        request.paymentMethod === PaymentMethod.CREDIT_CARD ||
                        consultationRequestDto.paymentMethod ===
                            PaymentMethod.CREDIT_CARD
                    ) {
                        consultationRequestDto.isVerified = true;
                    }
                    consultationRequestDto.finishedAt = moment()
                        .add(2, 'hour')
                        .toDate();
                    await this.sendFinishNotification(request, user);
                    await this.doctorsService.updateDoctorInaCall(
                        request.doctor.id,
                        false,
                    );
                    await this.sendPrescriptionEmail(request);
                    break;
                case ConsultationRequestStatus.CANCELLED:
                    consultationRequestDto = await this.cancelRequest(
                        consultationRequestDto,
                        request,
                        user,
                    );
            }
        }

        if (
            isFeeCollected &&
            request.paymentStatus !== PaymentStatus.PAID &&
            request.fees >= 1
        ) {
            await this.transactionsService.createTransaction(
                user.id,
                request.patient.id,
                request.doctor.id,
                request.id,
                request.paymentMethod,
                request.fees,
                TransactionStatus.SUCCEEDED,
                request.serviceProvider ? request.serviceProvider.id : null,
                request.kashfPercentage,
                TransactionKind.REQUEST_FEE,
            );
            await this.updateRequestStatusToPaid(request.id);
        }
        await this.consultationRequestsRepository.update(
            { id },
            consultationRequestDto,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ConsultationRequestMessagesKeys.UPDATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    private async sendProcedToPayNotification(
        request: ConsultationRequestEntity,
        user: UserDetailsDto,
    ) {
        if (user.role === RoleType.PATIENT) {
            return;
        }
        const doctorName = request.doctor.user.name;
        let notificationBodyAr = `تم قبول من د.${doctorName} طلبك اكمل لصفحه الدفع`;
        let notificationBodyEn = `Your Request is Accepted by Dr. ${doctorName} Procced to pay`;

        if (request.type === ConsultationType.ONLINE_CONSULTATION) {
            notificationBodyAr = `تم قبول طلبك من د. ${doctorName} اكمل لصفحه الدفع، سوف يتم الغاء الطلب في حالة عدم الدفع قبل موعد المكالمة ب ١٥ دقيقة`;
            notificationBodyEn = `Your Request is Accepted by Dr.${doctorName} Procced to pay, the request will be cancelled if you didn't pay 15 minute before the call`;
        }

        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.PROCEED_TO_PAY,
            },
            notificationTranslations: [
                {
                    title: 'تم قبول طلبك اكمل لصفحه الدفع',
                    body: notificationBodyAr,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Your Request is Accepted Procced to pay',
                    body: notificationBodyEn,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private async sendAcceptNotification(
        request: ConsultationRequestEntity,
        user: UserDetailsDto,
    ) {
        if (user.role === RoleType.PATIENT) {
            return;
        }
        const doctorName = request.doctor.user.name;

        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.FREE_REQUEST_ACCEPTED,
            },
            notificationTranslations: [
                {
                    title: 'تم قبول طلبك',
                    body: `تم قبول طلبك من د.${doctorName}.`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Your Request is Accepted',
                    body: `Your Request is Accepted by Dr.${doctorName}.`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private async sendStartNotification(
        request: ConsultationRequestEntity,
        user: UserDetailsDto,
    ) {
        if (user.role === RoleType.PATIENT) {
            return;
        }
        const doctorName = request.doctor.user.name;

        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.REQUEST_STARTED,
            },
            notificationTranslations: [
                {
                    title: 'تم بدئ طلبك',
                    body: `تم بدئ طلبك من د.${doctorName}.`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Your Request is Started',
                    body: `Your Request is Started by Dr.${doctorName}.`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private async sendFinishNotification(
        request: ConsultationRequestEntity,
        user: UserDetailsDto,
    ) {
        if (user.role === RoleType.PATIENT) {
            return;
        }
        const doctorName = request.doctor.user.name;

        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.REQUEST_FINISHED,
            },
            notificationTranslations: [
                {
                    title: 'تم إنهاء طلبك',
                    body: `تم إنهاء طلبك من د.${doctorName}.`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Your Request is Finished',
                    body: `Your Request is Finished by Dr.${doctorName}.`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private async sendPrescriptionEmail(request: ConsultationRequestEntity) {
        const patientUser = request.patient.user;
        const doctorUser = request.doctor.user;

        const downloadPromises = request.doctorNotesImages.map((key) =>
            this.s3Service.downloadImageFromS3(key),
        );
        const attachments = await Promise.all(downloadPromises);

        await this.emailService.sendEmailWithAttachmentAsBuffer(
            request.patient.user.email,
            'Prescription',
            new SimpleEmailBuilder()
                .addLine(`Dear ${patientUser.name}`)
                .addBreak()
                .addLine(
                    `Dr. ${doctorUser.name} has submitted a prescription for you .`,
                )
                .addBreak()
                .addText(request.prescription)
                .addBreak()
                .addLine('Thank you.').content,
            attachments,
        );

        attachments.forEach((attachment) =>
            fs.unlinkSync(attachment.path as string),
        );
        // await this.emailService.sendEmail(
        //     request.patient.user.email,
        //     'Prescription',
        //     new SimpleEmailBuilder()
        //         .addLine(`Dear ${patientUser.name}`)
        //         .addBreak()
        //         .addLine(
        //             `Dr. ${doctorUser.name} has submitted a prescription for you .`,
        //         )
        //         .addBreak()
        //         .addText(request.prescription)
        //         .addBreak()
        //         .addLine('Thank you.').content,
        // );
    }

    private async sendToSecretaryEmail(request: ConsultationRequestEntity) {
        const patientUser = request.patient.user;
        const doctorUser = request.doctor.user;

        await this.emailService.sendEmail(
            request.doctor.secretaryEmail,
            `New Consultation Request for Dr. ${doctorUser.name}`,
            new SimpleEmailBuilder()
                .addLine('Dears,')
                .addBreak()
                .addLine(
                    `I hope this message finds you well. I am reaching out to inform you of a new consultation request for ${doctorUser.name}.`,
                )
                .addBreak()
                .addLine('Patient Information:')
                .addLine(`Name: ${patientUser.name}`)
                .addLine(
                    `Request Date: ${moment(request.date).format(
                        'DD-MM-YYYY hh:mm A',
                    )}`,
                )
                .addLine(`KASHF ID: ${patientUser.id}`)
                .addBreak()
                .addLine('Status: Accepted Schedule')
                .addLine('Please assist in scheduling the consultation')
                .addBreak()
                .addLine('Thank you for your attention to this matter.')
                .content,
        );
    }

    private async cancelRequest(
        consultationRequestDto: UpdateConsultationRequestDto,
        request: ConsultationRequestEntity,
        user: UserDetailsDto,
    ): Promise<UpdateConsultationRequestDto> {
        let refundPercentage = 25;
        if (user.role === RoleType.DOCTOR) {
            refundPercentage = 0;
            this.sendRequestCancelledNotificationForPatient(request).catch(
                (error) => this.logger.error(JSON.stringify(error)),
            );
            await this.reminderService.deleteReminders(request);
            consultationRequestDto.cancelledBy = CancelledByType.DOCTOR;
        }
        if (user.role === RoleType.PATIENT) {
            this.sendRequestCancelledNotificationForDoctor(request).catch(
                (error) => this.logger.error(JSON.stringify(error)),
            );
            consultationRequestDto.cancelledBy = CancelledByType.PATIENT;
        }
        if (
            request.status === ConsultationRequestStatus.PAID &&
            request.transaction &&
            request.transaction.status === TransactionStatus.SUCCEEDED
        ) {
            const amountToRefund =
                request.fees - (request.fees * refundPercentage) / 100;
            const paymobTransaction =
                request.transaction.paymobTransactions.find(
                    (transaction) =>
                        transaction.status === TransactionStatus.SUCCEEDED,
                );
            const amountToRefundInCent = (amountToRefund * 100).toString();
            await this.paymentService.refundTransaction(
                request.transaction.id,
                paymobTransaction.paymobTransactionId.toString(),
                amountToRefundInCent,
                request.id,
            );
        }
        consultationRequestDto.canceledAt = moment().add(2, 'hour').toDate();

        return consultationRequestDto;
    }

    private async sendTimeChangedNotification(
        request: ConsultationRequestEntity,
        date: Date,
        from: Date,
    ) {
        const requestDate = new Date(`${date.toString()} ${from.toString()}`);
        const doctorName = request.doctor.user.name;
        const day = moment(requestDate).format('DD-MM-YYYY hh:mm A');
        const dayName = moment(requestDate).format('dddd');
        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.REQUEST_TIME_CHANGED,
            },
            notificationTranslations: [
                {
                    title: 'تم تغير ميعاد الحجز',
                    body: `قام د.${doctorName}  بتغير ميعاد الحجز الي يوم ${day}`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Online consultation time is changed',
                    body: `Dr.${doctorName} asked to change date and time to be ${dayName} ${day}`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private async notifyUserWithCode(request: ConsultationRequestEntity) {
        let textAr = `رمز طلب الزياره ${request.code}.`;
        let textEn = `Home visit consultation request code: ${request.code}.`;
        if (request.type === ConsultationType.CLINIC) {
            textAr = `رمز حجز العياده ${request.code}.`;
            textEn = `Clinic consultation request code: ${request.code}.`;
        }
        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.REQUEST_CODE,
            },
            notificationTranslations: [
                {
                    title: textAr,
                    body: textAr,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: textEn,
                    body: textEn,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private async sendRequestCancelledNotificationForPatient(
        request: ConsultationRequestEntity,
    ) {
        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.REQUEST_CANCELED,
                requestType: request.type.toString(),
                createdAt: moment(request.createdAt).format(
                    'YYYY-MM-DD HH:mm:ss',
                ),
                canceledAt: moment(request.canceledAt).format(
                    'YYYY-MM-DD HH:mm:ss',
                ),
            },
            notificationTranslations: [
                {
                    title: 'تم الغاء الحجز',
                    body: `قام د.${request.doctor.user.name} بالغاء الحجز`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'consultation request cancelled',
                    body: `Dr.${request.doctor.user.name} cancelled the request`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private async sendRequestCancelledNotificationForDoctor(
        request: ConsultationRequestEntity,
    ) {
        const notification: CreateNotificationDto = {
            userId: request.doctor.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.REQUEST_CANCELED,
                requestType: request.type.toString(),
                createdAt: moment(request.createdAt).format(
                    'YYYY-MM-DD HH:mm:ss',
                ),
                canceledAt: moment(request.canceledAt).format(
                    'YYYY-MM-DD HH:mm:ss',
                ),
            },
            notificationTranslations: [
                {
                    title: 'تم الغاء الطلب',
                    body: 'تم الغاء الطلب',
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'consultation request cancelled',
                    body: 'consultation request cancelled',
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.doctor.user.id,
                request.doctor.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    generateAgoraTokenOnTime(time: string = null, id: number): boolean {
        // const current = moment().add(2, 'hours');
        // const difference = Math.round(
        //     // eslint-disable-next-line import/namespace
        //     moment.duration(moment(time).diff(current)).asMinutes(),
        // );
        const date1 = moment(time);
        const now = moment().add(3, 'hours'); // .add(2, 'hours').add(15,"minutes");

        const difference = now.diff(date1, 'minutes');
        this.logger.log(now.format('DD-MM-YYYY HH:mm:ss'));
        this.logger.log(`difference => ${difference} with id of ${id}`);
        return difference === 1 || difference === -1;
    }

    async updateOnlineConsulationWithAgoraToken(): Promise<void> {
        const requests = await this.consultationRequestsRepository.find({
            where: [
                {
                    type: ConsultationType.ONLINE_CONSULTATION,
                    date: In([
                        moment().subtract(1, 'day').format('YYYY-MM-DD'),
                        moment().add(1, 'day').format('YYYY-MM-DD'),
                        moment().format('YYYY-MM-DD'),
                    ]),
                    status: ConsultationRequestStatus.PAID,
                    agoraToken: null,
                },
                {
                    type: ConsultationType.ONLINE_CONSULTATION,
                    date: In([
                        moment().subtract(1, 'day').format('YYYY-MM-DD'),
                        moment().add(1, 'day').format('YYYY-MM-DD'),
                        moment().format('YYYY-MM-DD'),
                    ]),
                    status: ConsultationRequestStatus.ACCEPTED,
                    isFreeRequest: true,
                    agoraToken: null,
                },
            ],
            relations: ['doctor', 'doctor.user', 'patient', 'patient.user'],
        });
        this.logger.log(
            `TO=>${JSON.stringify(requests.map((request) => request.id))}`,
        );
        await Promise.all(
            requests.map(async (request) => {
                const time = request.from
                    ? `${request.date.toString()} ${request.from.toString()}`
                    : null;

                const shouldGenerateToken = time
                    ? this.generateAgoraTokenOnTime(time, request.id)
                    : false;

                // Only generate if token is missing AND time is right
                if (shouldGenerateToken && !request.agoraToken) {
                    this.logger.log('send token');
                    const expirationTimeInSeconds = (request.duration + 1) * 60;
                    const agoraToken = this.agoraService.getAccessToken(
                        request.id.toString(),
                        expirationTimeInSeconds,
                    );

                    await this.consultationRequestsRepository.update(
                        { id: request.id },
                        { agoraToken },
                    );

                    await this.sendNotificationONCallReady(request);
                }
            }),
        );
    }

    private async sendNotificationONCallReady(
        request: ConsultationRequestEntity,
    ) {
        const notificationForPatient: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.CALL_READY,
            },
            notificationTranslations: [
                {
                    title: 'ستبدأ المكالمه الان',
                    body:
                        'ستبدأ المكالمه الان. من فضلك تأكد من وجود اتصال جيد بالانترنت',
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Online consultation is started',
                    body:
                        'Online consultation is started. Please make sure that you have a good internet connection.',
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const notificationForDoctor: CreateNotificationDto = {
            userId: request.doctor.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.CALL_READY,
            },
            notificationTranslations: [
                {
                    title: 'ستبدأ المكالمه الان',
                    body:
                        'ستبدأ المكالمه الان. من فضلك تأكد من وجود اتصال جيد بالانترنت',
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Online consultation is started',
                    body:
                        'Online consultation is started. Please make sure that you have a good internet connection.',
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const [
            createdNotificationForPatient,
            createdNotificationForDoctor,
        ] = await Promise.all([
            this.notificationsService.createNotification(
                notificationForPatient,
            ),
            this.notificationsService.createNotification(notificationForDoctor),
        ]);
        await this.notificationsService.sendNotification(
            createdNotificationForPatient.createdId,
            request.patient.user.id,
            request.patient.user.appLanguage,
        );
        await this.notificationsService.sendNotification(
            createdNotificationForDoctor.createdId,
            request.doctor.user.id,
            request.doctor.user.appLanguage,
        );
    }

    private async checkifUserCanUpdateStatus(
        user: UserDetailsDto,
        status: ConsultationRequestStatus,
        request: ConsultationRequestEntity,
        lang: string,
    ): Promise<boolean> {
        const isStatusChanged = status !== request.status;
        if (!isStatusChanged) {
            return isStatusChanged;
        }
        let isAllowedToUpdateStatus = false;
        if (user.role === RoleType.DOCTOR) {
            const doctorAllowedStatus = [
                ConsultationRequestStatus.ACCEPTED,
                ConsultationRequestStatus.FINISHED,
                ConsultationRequestStatus.CANCELLED,
                ConsultationRequestStatus.STARTED,
            ];
            isAllowedToUpdateStatus = doctorAllowedStatus.includes(status);
        }
        if (user.role === RoleType.PATIENT) {
            const patiantAllowedStatus = [
                ConsultationRequestStatus.ACCEPTED,
                ConsultationRequestStatus.CANCELLED,
                ConsultationRequestStatus.STARTED,
            ];
            isAllowedToUpdateStatus = patiantAllowedStatus.includes(status);
        }
        if (!isAllowedToUpdateStatus) {
            throw new HttpException(
                {
                    message: 'user is not allowed to update status',
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        if (
            request.status === ConsultationRequestStatus.CANCELLED ||
            request.status === ConsultationRequestStatus.FINISHED ||
            request.status === ConsultationRequestStatus.EXPIRED
        ) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.UPDATE_FAILED,
                        { lang },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        return isStatusChanged;
    }

    async getAllDoctorRequests(
        filter: IFilter,
        lang: string,
    ): Promise<ConsultationRequestDto[]> {
        const query = this.consultationRequestsRepository
            .createQueryBuilder('consultationRequestEntity')
            .leftJoinAndSelect('consultationRequestEntity.patient', 'patient')
            .leftJoinAndSelect('consultationRequestEntity.doctor', 'doctor')
            .leftJoinAndSelect(
                'consultationRequestEntity.serviceProvider',
                'serviceProvider',
            )
            .leftJoinAndSelect('patient.user', 'user')
            .select([
                'consultationRequestEntity.id',
                'consultationRequestEntity.agoraToken',
                'consultationRequestEntity.date',
                'consultationRequestEntity.from',
                'consultationRequestEntity.duration',
                'consultationRequestEntity.status',
                'consultationRequestEntity.type',
                'consultationRequestEntity.patientNotes',
                'consultationRequestEntity.doctorNotes',
                'consultationRequestEntity.doctorNotesImages',
                'consultationRequestEntity.prescription',
                'consultationRequestEntity.diagnosis',
                'consultationRequestEntity.isFeeCollected',
                'consultationRequestEntity.isKashfFeePaid',
                'consultationRequestEntity.patientAddress',
                'consultationRequestEntity.patientLocation',
                'consultationRequestEntity.patientLocationAddress',
                'consultationRequestEntity.forOtherPatient',
                'consultationRequestEntity.otherPatientName',
                'consultationRequestEntity.otherPatientNumber',
                'consultationRequestEntity.otherPatientDateOfBirth',
                'consultationRequestEntity.otherPatientGender',
                'consultationRequestEntity.paymentMethod',
                'consultationRequestEntity.createdAt',
                'consultationRequestEntity.canceledAt',
                'user.id',
                'user.name',
                'user.avatar',
                'user.phone',
                'user.gender',
                'patient.id',
                'patient.chronicDiseases',
                'patient.disabilities',
                'patient.injuries',
                'patient.surgeries',
                'patient.dateOfBirth',
                'patient.bloodGroup',
                'patient.maritalStatus',
                'patient.isSmoking',
                'patient.allergies',
                'doctor.id',
                'serviceProvider.id',
            ])
            .leftJoinAndSelect('patient.medications', 'medications')
            .leftJoinAndSelect(
                'medications.translations',
                'medicationsTranslations',
                'medicationsTranslations.languageCode = :lang',
                { lang },
            )
            .orderBy('consultationRequestEntity.createdAt', 'DESC');

        if (filter.doctorId) {
            query.andWhere('doctor.id = :doctorId', {
                doctorId: filter.doctorId,
            });
        }
        if (filter.patientId) {
            query.andWhere('patient.id = :patientId', {
                patientId: filter.patientId,
            });
        }
        if (filter.serviceProviderId) {
            query.andWhere('serviceProvider.id = :serviceProviderId', {
                serviceProviderId: filter.serviceProviderId,
            });
        }

        if (filter.date) {
            query.andWhere('consultationRequestEntity.date = :date', {
                date: filter.date,
            });
        }
        const requests = await query.getMany();
        return requests.map((request) =>
            this.consultationRequestsMapper.fromEntityToDTO(
                ConsultationRequestDto,
                request,
            ),
        );
    }

    async downloadRequests(
        providerId: string,
        httpQueryString: string,
    ): Promise<string> {
        let query = this.consultationRequestsRepository
            .createQueryBuilder('request')
            .leftJoinAndSelect('request.patient', 'patient')
            .leftJoinAndSelect('request.doctor', 'doctor')
            .leftJoinAndSelect('patient.user', 'patientUser')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .select([
                'request.id',
                'request.type',
                'request.date',
                'request.from',
                'request.status',
                'patient.id',
                'patientUser.id',
                'patientUser.name',
                'patientUser.phone',
                'doctor.id',
                'doctorUser.id',
                'doctorUser.name',
                'doctorUser.phone',
            ])
            .orderBy('request.date', 'DESC')
            .addOrderBy('request.from', 'DESC');

        if (providerId) {
            query
                .leftJoinAndSelect('request.serviceProvider', 'serviceProvider')
                .where('serviceProvider.id = :providerId', {
                    providerId: parseInt(providerId, 10),
                });
        }

        if (httpQueryString) {
            query = this.buildGetRequestsQuery(query, httpQueryString);
        }

        const requests = await query.getMany();

        const mappedRequests = requests.map((request) =>
            this.consultationRequestsMapper.fromEntityToDownloadData(
                DownloadConsultationRequestDto,
                request,
            ),
        );

        const fields = [
            'id',
            'consultationType',
            'date',
            'time',
            'status',
            'doctorName',
            'doctorMobileNumber',
            'patientName',
            'patientMobileNumber',
        ];
        return this.dataDownloadService.downloadCsv(fields, mappedRequests);
    }

    async getAllRequestsForAdmin(
        user: UserDetailsDto,
        httpQueryString: string,
        pageOptionsDto: PageOptionsDto,
    ): Promise<ConsultationRequestsPageDto> {
        let query = this.consultationRequestsRepository
            .createQueryBuilder('request')
            .leftJoinAndSelect('request.patient', 'patient')
            .leftJoinAndSelect('request.doctor', 'doctor')
            .leftJoinAndSelect('patient.user', 'patientUser')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .leftJoinAndSelect('request.serviceProvider', 'serviceProvider')
            .select([
                'request.id',
                'request.type',
                'request.date',
                'request.from',
                'request.status',
                'request.startedAt',
                'request.acceptedAt',
                'request.finishedAt',
                'request.canceledAt',
                'request.createdAt',
                'request.patientNotes',
                'request.doctorNotes',
                'patient.id',
                'patientUser.id',
                'patientUser.name',
                'patientUser.phone',
                'serviceProvider',
                'doctor.id',
                'doctorUser.id',
                'doctorUser.name',
                'doctorUser.phone',
            ])
            .orderBy('request.date', 'DESC')
            .addOrderBy('request.from', 'DESC');

        if (user.role === RoleType.SERVICE_PROVIDER) {
            query.andWhere('serviceProvider.id = :providerId', {
                providerId: user.serviceProvider.id,
            });
        }

        if (httpQueryString) {
            query = this.buildGetRequestsQuery(query, httpQueryString);
        }

        const [requests, pageMetaDto] = await query.paginate(pageOptionsDto);

        const mappedRequests = requests.map((request) =>
            this.consultationRequestsMapper.fromEntityToDTO(
                ConsultationRequestDto,
                request,
            ),
        );

        return new ConsultationRequestsPageDto(mappedRequests, pageMetaDto);
    }

    async getAllPatientRequests(
        userId: number,
        filter: IFilter,
        lang: string,
    ): Promise<ConsultationRequestDto[]> {
        const query = this.consultationRequestsRepository
            .createQueryBuilder('consultationRequestEntity')
            .leftJoinAndSelect('consultationRequestEntity.patient', 'patient')
            .leftJoinAndSelect('consultationRequestEntity.doctor', 'doctor')
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.user', 'user')
            .select([
                'consultationRequestEntity.id',
                'consultationRequestEntity.agoraToken',
                'consultationRequestEntity.date',
                'consultationRequestEntity.from',
                'consultationRequestEntity.createdAt',
                'consultationRequestEntity.canceledAt',
                'consultationRequestEntity.from',
                'consultationRequestEntity.duration',
                'consultationRequestEntity.status',
                'consultationRequestEntity.type',
                'consultationRequestEntity.paymentMethod',
                'consultationRequestEntity.doctorNotes',
                'consultationRequestEntity.doctorNotesImages',
                'consultationRequestEntity.prescription',
                'consultationRequestEntity.diagnosis',
                'user.id',
                'user.name',
                'user.avatar',
                'user.gender',
                'patient.id',
                'doctor.id',
                'grade',
                'gradeTranslations',
                'specialities',
                'specialityTranslations',
            ])
            .orderBy('consultationRequestEntity.date', 'DESC')
            .addOrderBy('consultationRequestEntity.from', 'DESC');

        if (filter.patientId) {
            query.andWhere('patient.id = :patientId', {
                patientId: filter.patientId,
            });
        }
        const requests = await query.getMany();

        await Promise.all(
            requests.map(async (request: any) => {
                const doctorDto = this.doctorsMapper.fromEntityToDTO(
                    DoctorDto,
                    request.doctor,
                );
                doctorDto.grade = doctorDto.grade
                    ? {
                        id: doctorDto.grade.id,
                        title: doctorDto.grade.translations[0]
                            ? doctorDto.grade.translations[0].title
                            : '',
                    }
                    : {
                        id: null,
                        title: '',
                    };
                doctorDto.specialities = doctorDto.specialities
                    ? doctorDto.specialities.map((speciality) => ({
                        id: speciality.id,
                        title: speciality.translations[0]
                            ? speciality.translations[0].title
                            : '',
                    }))
                    : [];

                request.doctor = doctorDto;
                if (request.status === ConsultationRequestStatus.FINISHED) {
                    request.isRated = await this.ratingService.getIsRequestRated(
                        request.id,
                        userId,
                    );
                }
            }),
        );

        return requests.map((request) =>
            this.consultationRequestsMapper.fromEntityToDTO(
                ConsultationRequestDto,
                request,
            ),
        );
    }

    async findOne(id: number): Promise<ConsultationRequestEntity> {
        return this.consultationRequestsRepository.findOne({
            where: { id },
        });
    }

    async getRequest(
        id: number,
        lang: string,
    ): Promise<ConsultationRequestDto> {
        const query = this.consultationRequestsRepository
            .createQueryBuilder('consultationRequestEntity')
            .leftJoinAndSelect('consultationRequestEntity.patient', 'patient')
            .leftJoinAndSelect('patient.medications', 'medications')
            .leftJoinAndSelect(
                'medications.translations',
                'medicationsTranslations',
                'medicationsTranslations.languageCode = :lang',
                { lang },
            )
            // .leftJoinAndSelect(
            //     'consultationRequestEntity.choosenSpeciality',
            //     'speciality',
            // )
            // .leftJoinAndSelect(
            //     'speciality.translations',
            //     'specialityTranslations',
            //     'specialityTranslations.languageCode = :lang',
            //     { lang },
            // )
            .leftJoinAndSelect(
                'consultationRequestEntity.serviceProvider',
                'serviceProvider',
            )
            .leftJoinAndSelect('serviceProvider.user', 'serviceProviderUser')
            .leftJoinAndSelect(
                'consultationRequestEntity.transaction',
                'transaction',
            )
            .leftJoinAndSelect('patient.user', 'user')
            .leftJoinAndSelect('consultationRequestEntity.clinic', 'clinic')
            .leftJoinAndSelect(
                'clinic.translations',
                'clinicTranslations',
                'clinicTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('consultationRequestEntity.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'userDoctor')
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .select([
                'consultationRequestEntity.id',
                'consultationRequestEntity.agoraToken',
                'consultationRequestEntity.isFreeRequest',
                'consultationRequestEntity.date',
                'consultationRequestEntity.from',
                'consultationRequestEntity.canceledAt',
                'consultationRequestEntity.createdAt',
                'consultationRequestEntity.duration',
                'consultationRequestEntity.status',
                'consultationRequestEntity.type',
                'consultationRequestEntity.fees',
                'consultationRequestEntity.discountAmount',
                'consultationRequestEntity.isKashfFeePaid',
                'consultationRequestEntity.promoCode',
                'consultationRequestEntity.code',
                'consultationRequestEntity.patientNotes',
                'consultationRequestEntity.patientAddress',
                'consultationRequestEntity.patientLocation',
                'consultationRequestEntity.patientLocationAddress',
                'consultationRequestEntity.forOtherPatient',
                'consultationRequestEntity.otherPatientName',
                'consultationRequestEntity.otherPatientNumber',
                'consultationRequestEntity.otherPatientDateOfBirth',
                'consultationRequestEntity.otherPatientGender',
                'consultationRequestEntity.paymentMethod',
                'consultationRequestEntity.paymentStatus',
                'consultationRequestEntity.kashfPercentage',
                'consultationRequestEntity.choosenSpecialityId',
                'transaction.id',
                'transaction.status',
                'patient.id',
                'patient.chronicDiseases',
                'patient.disabilities',
                'patient.injuries',
                'patient.surgeries',
                'patient.dateOfBirth',
                'patient.bloodGroup',
                'patient.maritalStatus',
                'patient.isSmoking',
                'patient.allergies',
                'user.id',
                'user.avatar',
                'user.phone',
                'user.name',
                'user.gender',
                'user.averageRating',
                'clinic.id',
                'clinicTranslations',
                // 'speciality.id',
                // 'specialityTranslations',
                'doctor.id',
                'doctor.isAcceptCashOnhomeVisit',
                'userDoctor.id',
                'userDoctor.name',
                'userDoctor.avatar',
                'userDoctor.averageRating',
                'specialities',
                'specialityTranslations',
                'grade',
                'gradeTranslations',
                'serviceProvider.id',
                'serviceProviderUser.name',
            ])
            .where('consultationRequestEntity.id = :id', {
                id,
            });

        const request = await query.getOne();
        if (!request) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.REQUEST_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const requestDto = this.consultationRequestsMapper.fromEntityToDTO(
            ConsultationRequestDto,
            request,
        );

        const doctorDto = this.doctorsMapper.fromEntityToDTO(
            DoctorDto,
            request.doctor,
        );

        doctorDto.grade = doctorDto.grade
            ? {
                id: doctorDto.grade.id,
                title: doctorDto.grade.translations[0]
                    ? doctorDto.grade.translations[0].title
                    : '',
            }
            : {
                id: null,
                title: '',
            };

        doctorDto.specialities = doctorDto.specialities
            ? doctorDto.specialities.map((speciality) => ({
                id: speciality.id,
                title: speciality.translations[0]
                    ? speciality.translations[0].title
                    : '',
            }))
            : [];

        requestDto.doctor = doctorDto;

        if (request.type === ConsultationType.CLINIC && request.clinic) {
            requestDto.clinic = {
                id: request.clinic.id,
                name: request.clinic.translations[0]
                    ? request.clinic.translations[0].name
                    : '',
            };
        }
        // to be removed after fixing the frontend bug
        if (!requestDto.patient.medications) {
            requestDto.patient.medications = [];
        }
        return requestDto;
    }

    async getRequestForAdmin(
        id: number,
        lang: string,
    ): Promise<ConsultationRequestDto> {
        const query = this.consultationRequestsRepository
            .createQueryBuilder('request')
            .leftJoinAndSelect('request.patient', 'patient')
            .leftJoinAndSelect('request.doctor', 'doctor')
            .leftJoinAndSelect('patient.user', 'patientUser')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .select([
                'request.id',
                'request.type',
                'request.date',
                'request.from',
                'request.canceledAt',
                'request.createdAt',
                'request.status',
                'request.patientNotes',
                'request.doctorNotes',
                'patient.id',
                'patientUser.id',
                'patientUser.name',
                'patientUser.phone',
                'doctor.id',
                'doctorUser.id',
                'doctorUser.name',
                'doctorUser.phone',
            ])
            .where('request.id = :id', { id })
            .orderBy('request.date', 'DESC')
            .addOrderBy('request.from', 'DESC');

        const request = await query.getOne();
        if (!request) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.REQUEST_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.consultationRequestsMapper.fromEntityToDTO(
            ConsultationRequestDto,
            request,
        );
    }

    addPaymentCondition(checkIsPending: boolean) {
        return checkIsPending ? `
        AND  payment_status = '${PaymentStatus.PENDING}'
        ` : ``;
    }

    async markScheduledRequestsAsExpired(checkPayment: boolean = false): Promise<number[]> {
        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set expired_at = now(),
            status     = 'Expired'
        where now() > created_at + interval '24 hour'
          AND status = '${ConsultationRequestStatus.REQUESTED}' ${this.addPaymentCondition(checkPayment)}
            RETURNING id
    `);

        this.logger.log(`schedule_expiration_requested=>${JSON.stringify(requestsIds)}`);
        return requestsIds;
    }

    async markNowOnlineRequestsAsExpired(checkPayment: boolean = false): Promise<number[]> {
        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set expired_at = now(),
            status     = 'Expired'
        where now() > created_at + interval '3 minute'
          AND status = '${ConsultationRequestStatus.REQUESTED}'
          AND type = '${ConsultationType.ONLINE_CONSULTATION}' ${this.addPaymentCondition(checkPayment)}
          AND "from" IS Null
            RETURNING id
    `);
        this.logger.log(`now_expiration_requested=>${JSON.stringify(requestsIds)}`);
        return requestsIds;
    }

    async markNowOnlineRequestsAsExpiredAfterAccept(checkPayment: boolean = false): Promise<number[]> {
        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set expired_at = now(),
            status     = 'Expired'
        where now() > accepted_at + interval '15 minute'
          AND status = '${ConsultationRequestStatus.ACCEPTED}'
          AND type = '${ConsultationType.ONLINE_CONSULTATION}' ${this.addPaymentCondition(checkPayment)}
          AND "from" IS Null
            RETURNING id
    `);
        this.logger.log(`now_expiration_accepted=>${JSON.stringify(requestsIds)}`);
        return requestsIds;
    }

    async markOnlineRequestsAsExpiredAfterAccept(checkPayment: boolean = false): Promise<number[]> {
        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set expired_at = now(),
            status     = 'Expired'
        where now() + interval '120 minute'
            > "date" + "from" - interval '15 minute'
          AND status = '${ConsultationRequestStatus.ACCEPTED}'
          AND type = '${ConsultationType.ONLINE_CONSULTATION}' ${this.addPaymentCondition(checkPayment)}
          AND "from" IS NOT Null
            RETURNING id
    `);
        //

        this.logger.log(`schedule_expiration_accepted=>${JSON.stringify(requestsIds)}`);
        return requestsIds;
    }

    async markNowHomeVisitRequestsAsExpired(checkPayment: boolean = false): Promise<number[]> {
        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set expired_at = now(),
            status     = 'Expired'
        where now() > created_at + interval '5 minute'
          AND status = '${ConsultationRequestStatus.REQUESTED}'
          AND type = '${ConsultationType.HOME_VISIT}' ${this.addPaymentCondition(checkPayment)}
          AND "from" IS Null
            RETURNING id
    `);
        return requestsIds;
    }

    async markRequestsAsExpiredIfNotPaid(): Promise<number[]> {
        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set expired_at = now(),
            status     = 'Expired'
        where now() > created_at + interval '15 minute'
          AND payment_status = '${PaymentStatus.PENDING}'
          AND status <> '${ConsultationRequestStatus.EXPIRED}'
          AND status <> '${ConsultationRequestStatus.FINISHED}'
          AND status <> '${ConsultationRequestStatus.CANCELLED}'
          AND status <> '${ConsultationRequestStatus.PAID}'
          AND status <> '${ConsultationRequestStatus.ACCEPTED}'
            RETURNING id
    `);
        // this.logger.log(`schedule_expiration_accepted=>${JSON.stringify(requestsIds)}`);
        return requestsIds;
    }

    async getDoctorRequestsByDate(
        doctorId: number,
        date: Date,
    ): Promise<ConsultationRequestDto[]> {
        const consultationEntities = await this.consultationRequestsRepository.getDoctorConsultationsByDate(
            doctorId,
            date,
        );

        return consultationEntities.map((consultationEntity) =>
            this.consultationRequestsMapper.fromEntityToDTO(
                CreateConsultationRequestDto,
                consultationEntity,
            ),
        );
    }

    async updateRequestStatusToPendingPayment(
        id: number,
        transactionId: number,
    ): Promise<void> {
        await this.consultationRequestsRepository.update(
            { id },
            {
                transaction: {
                    id: transactionId,
                },
                status: ConsultationRequestStatus.PENDINGPAYMENT,
            },
        );
    }

    async updateRequestStatusToPaid(id: number): Promise<void> {
        const request = await this.consultationRequestsRepository.findOne({
            where: { id },
            relations: ['patient', 'doctor', 'doctor.user', 'patient.user'],
        });
        if (!request) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.REQUEST_NOT_FOUND,
                        {
                            lang: request.patient.user.appLanguage,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        await this.consultationRequestsRepository.update(
            { id },
            {
                status: ConsultationRequestStatus.PAID,
                paymentStatus: PaymentStatus.PAID,
            },
        );
        this.notifyDoctorWhenPatientPaid(request);
    }

    private async notifyDoctorWhenPatientPaid(
        request: ConsultationRequestEntity,
    ) {
        const patientName = request.patient.user.name;

        const notification: CreateNotificationDto = {
            userId: request.doctor.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.NOTIFIY_DOCTOR_AFTER_PAID,
            },
            notificationTranslations: [
                {
                    title: ` ${patientName} تمت عمليه الدفع لطلب جديد من قبل `,
                    body: `${patientName} تمت عمليه الدفع لطلب جديد من قبل`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: `New request is paid by ${patientName}.`,
                    body: `New request is paid by ${patientName}.`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification = await this.notificationsService.createNotification(
            notification,
        );
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.doctor.user.id,
                request.doctor.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    async isPendingKashfFees(id: number): Promise<boolean> {
        const requests = await this.consultationRequestsRepository.count({
            where: {
                isFeeCollected: true,
                isKashfFeePaid: false,
                paymentStatus: PaymentStatus.PAID,
                paymentMethod: PaymentMethod.CASH,
                doctor: { id },
            },
        });
        return requests != 0;
    }

    async setKashfFeesCollected(id: number) {
        await this.consultationRequestsRepository.update(
            { id },
            { isKashfFeePaid: true },
        );
    }

    async updateRequestStatusToRefunded(id: number): Promise<void> {
        await this.consultationRequestsRepository.update(
            { id },
            {
                paymentStatus: PaymentStatus.REFUNDED,
            },
        );
    }

    async verifyConsultationRequest(
        id: number,
        code: string,
        doctorId: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const request = await this.consultationRequestsRepository.findOne({
            where: { id, doctor: { id: doctorId } },
            select: ['code'],
        });
        if (!request) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.REQUEST_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        if (code !== request.code) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.WRONG_REQUEST_CODE,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        await this.consultationRequestsRepository.update(
            { id, doctor: { id: doctorId } },
            {
                isVerified: true,
            },
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ConsultationRequestMessagesKeys.CORRECT_REQUEST_CODE,
                {
                    lang,
                },
            ),
        };
    }

    async updateRequestDiscountAmount(
        id: number,
        discountAmount: number,
        promoCode: string,
    ): Promise<void> {
        await this.consultationRequestsRepository.update(
            { id },
            {
                discountAmount,
                promoCode,
            },
        );
    }

    async getMonthlyDoctorRequests(
        doctorId: number,
    ): Promise<ConsultationRequestEntity[]> {
        const today = moment().add(2, 'hour').toDate();
        return this.consultationRequestsRepository.find({
            where: {
                doctor: {
                    id: doctorId,
                },
                date: MoreThanOrEqual(today),
                status: ConsultationRequestStatus.ACCEPTED,
            },
            select: ['id', 'date'],
        });
    }

    buildGetRequestsQuery(
        query: SelectQueryBuilder<ConsultationRequestEntity>,
        httpQueryString: string,
    ): SelectQueryBuilder<ConsultationRequestEntity> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }

        if (httpQueryObject.search) {
            query.andWhere(
                '(request.id || patientUser.name || patientUser.phone || doctorUser.name || doctorUser.phone) ILIKE :searchKey',
                {
                    searchKey: `%${httpQueryObject.search.value.trim()}%`,
                },
            );
        }

        if (httpQueryObject.filters) {
            httpQueryObject.filters.forEach((filter) => {
                switch (filter.type) {
                    case FilterType.FIXED:
                        switch (filter.by) {
                            case FilterByType.CONSULTATION_TYPE:
                                query.andWhere('request.type = :reqType', {
                                    reqType: filter.value,
                                });
                                break;
                            case FilterByType.DOCTOR:
                                query.andWhere('doctor.id = :id', {
                                    id: filter.value,
                                });
                                break;
                            case FilterByType.PATIENT:
                                query.andWhere('patient.id = :patientId', {
                                    patientId: filter.value,
                                });
                        }
                    case FilterType.RANGE:
                        switch (filter.by) {
                            case FilterByType.DATE:
                                query.andWhere(
                                    'request.date between :min AND :max',
                                    {
                                        min: filter.min,
                                        max: filter.max,
                                    },
                                );
                        }
                }
            });
        }

        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.DATE:
                    query.orderBy('request.date', httpQueryObject.sort.type);
                    break;
                case SortByType.CONSULTATION_TYPE:
                    query.orderBy('request.type', httpQueryObject.sort.type);
            }
        }

        return query;
    }

    async getExpiredRequests(
        requestsIds: { id: number }[],
    ): Promise<ConsultationRequestEntity[]> {
        const ids: number[] = requestsIds.map((request) => request.id);
        if (ids.length === 0) {
            throw new HttpException(
                {
                    status: HttpStatus.NOT_FOUND,
                    error: 'not users to send them new notifications',
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const query = this.consultationRequestsRepository
            .createQueryBuilder('request')
            .leftJoinAndSelect('request.patient', 'patient')
            .leftJoinAndSelect('patient.user', 'user')
            .select(['request.id', 'patient.id', 'user.id', 'user.appLanguage'])
            .where('request.id IN(:...ids)', { ids });

        return query.getMany();
    }

    async getReminderRequests(
        requestsIds: number[],
    ): Promise<ConsultationRequestEntity[]> {

        if (requestsIds.length === 0) {
            throw new HttpException(
                {
                    status: HttpStatus.NOT_FOUND,
                    error: 'not users to send them new notifications',
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const query = this.consultationRequestsRepository
            .createQueryBuilder('request')
            .leftJoinAndSelect('request.patient', 'patient')
            .leftJoinAndSelect('patient.user', 'user')
            .select(['request.id', 'patient.id', 'user.id', 'user.appLanguage'])
            .where('request.id IN(:...ids)', { ids: requestsIds });

        return query.getMany();
    }

    async sendExpiredNotificationForUserNotResponding(
        requestsIds: any,
    ): Promise<void> {
        const requests = await this.getExpiredRequests(requestsIds);
        const notificationTranslations = [
            {
                title: `.تم الغاء الطلب`,
                body: `تم إلغاء الطلب لأنك لم تقم باتمام عمليه الدفع لطلبك في الوقت المناسب`,
                languageCode: AvailableLanguageCodes.ar,
            },
            {
                title: `The request is cancelled.`,
                body: `The request was cancelled as you did not proceed to pay for your request in due time.`,
                languageCode: AvailableLanguageCodes.en,
            },
        ];
        await Promise.all(
            requests.map((request) => {
                this.sendExpiredNotification(
                    request,
                    notificationTranslations,
                ).catch((error) => this.logger.error(JSON.stringify(error)));
            }),
        );
    }

    async SendExpirationNotifications(requestsIds: any): Promise<void> {
        const requests = await this.getExpiredRequests(requestsIds);
        const notificationTranslations = [
            {
                title: `.تم الغاء الطلب`,
                body: `تم إلغاء الطلب لأن الطبيب لم يرد على طلبك في الوقت المناسب`,
                languageCode: AvailableLanguageCodes.ar,
            },
            {
                title: `The request is cancelled.`,
                body: `The request was cancelled as the doctor did not react to your request in due time.`,
                languageCode: AvailableLanguageCodes.en,
            },
        ];
        await Promise.all(
            requests.map((request) => {
                this.sendExpiredNotification(
                    request,
                    notificationTranslations,
                ).catch((error) => this.logger.error(JSON.stringify(error)));
            }),
        );
    }

    async sendReminderNotifications(requestIds: { id: number, type: ReminderType }[]): Promise<void> {
        const ids: number[] = requestIds.map((request) => request.id);
        const requests = await this.getReminderRequests(ids);

        await Promise.all(
            requests.map((request, index) => {
                let notificationTranslations = [];
                switch (requestIds[index].type) {
                    case ReminderType.FIFTEEN_MINUTES_BEFORE:
                        notificationTranslations = [
                            {
                                title: `اقترب موعدك بخمسة عشر دقائق فقط.`,
                                body: `هذا تذكير بأن موعدك القادم على بُعد دقائق قليلة فقط. نحن نرغب في التأكد من أنك حصلت على جميع المعلومات التي تحتاجها وأنك مستعد تمامًا لزيارتك.`,
                                languageCode: AvailableLanguageCodes.ar,
                            },
                            {
                                title: `Your Appointment is Just 15 Minutes Away!`,
                                body: `This is a reminder that your upcoming appointment is just about to start. We want to ensure you have all the information you need and are well-prepared for your visit.`,
                                languageCode: AvailableLanguageCodes.en,
                            },
                        ];
                        break;
                    case ReminderType.TWO_HOURS_BEFORE:
                        notificationTranslations = [
                            {
                                title: `اقترب موعدك بساعتين.`,
                                body: `هذا تذكير بأن موعدك القادم على بُعد ساعتين فقط. نحن نرغب في التأكد من أنك حصلت على جميع المعلومات التي تحتاجها وأنك مستعد تمامًا لزيارتك.`,
                                languageCode: AvailableLanguageCodes.ar,
                            },
                            {
                                title: `Your Appointment is Just 2 Hours Away!`,
                                body: `This is a reminder that your upcoming appointment is just 2 Hours Away. We want to ensure you have all the information you need and are well-prepared for your visit.`,
                                languageCode: AvailableLanguageCodes.en,
                            },
                        ];
                        break;
                    case ReminderType.ONE_DAY_BEFORE:
                        notificationTranslations = [
                            {
                                title: `اقترب موعدك بيوم واحد فقط.`,
                                body: `هذا تذكير بأن موعدك القادم على بُعد أيام قليلة فقط. نحن نرغب في التأكد من أنك حصلت على جميع المعلومات التي تحتاجها وأنك مستعد تمامًا لزيارتك.`,
                                languageCode: AvailableLanguageCodes.ar,
                            },
                            {
                                title: `Your Appointment is Just 1 Day Away!`,
                                body: `This is a reminder that your upcoming appointment is just around the corner. We want to ensure you have all the information you need and are well-prepared for your visit.`,
                                languageCode: AvailableLanguageCodes.en,
                            },
                        ];
                        break;

                }
                this.sendReminderNotification(
                    request,
                    notificationTranslations,
                ).catch((error) => this.logger.error(JSON.stringify(error)));
            }),
        );
    }

    private async sendExpiredNotification(
        request: ConsultationRequestEntity,
        notificationTranslations: Array<{
            title: string;
            body: string;
            languageCode: AvailableLanguageCodes;
        }>,
    ) {
        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.NOT_PAIED_EXPIRED_REQUEST,
            },
            notificationTranslations,
        };
        const createdNotification = await this.notificationsService.createNotification(
            notification,
        );
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private async sendReminderNotification(
        request: ConsultationRequestEntity,
        notificationTranslations: Array<{
            title: string;
            body: string;
            languageCode: AvailableLanguageCodes;
        }>,
    ) {
        const notification: CreateNotificationDto = {
            userId: request.patient.user.id,
            data: {
                requestId: request.id.toString(),
                type: NotificationTyps.NOTIFY_PATIENT_REMINDER,
            },
            notificationTranslations,
        };
        const createdNotification = await this.notificationsService.createNotification(
            notification,
        );
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                request.patient.user.id,
                request.patient.user.appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    getRequestTimeForAsap(): Date {
        const nowMinutes = moment().minutes();
        const nowHours = moment().hours();

        if (nowMinutes < 15) {
            return moment(`${nowHours}:00:00`, 'HH:mm:ss').toDate();
        }
        if (nowMinutes < 30) {
            return moment(`${nowHours}:15:00`, 'HH:mm:ss').toDate();
        }
        if (nowMinutes < 45) {
            return moment(`${nowHours}:30:00`, 'HH:mm:ss').toDate();
        }
        return moment(`${nowHours}:45:00`, 'HH:mm:ss').toDate();
    }

    async getUnsettledPaymentRequests(): Promise<ConsultationRequestEntity[]> {
        const date = new Date();
        date.setHours(date.getHours() - 1);
        const query = this.consultationRequestsRepository
            .createQueryBuilder('consultationRequestEntity')
            .leftJoinAndSelect('consultationRequestEntity.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .select([
                'consultationRequestEntity.id',
                'consultationRequestEntity.paymentStatus',
                'consultationRequestEntity.isFeeCollected',
                'consultationRequestEntity.isKashfFeePaid',
                'consultationRequestEntity.paymentMethod',
                'consultationRequestEntity.updatedAt',
                'doctor.id',
                'doctorUser.id',
                'doctorUser.appLanguage',
            ])
            .where('consultationRequestEntity.isFeeCollected = true')
            .andWhere(
                'consultationRequestEntity.paymentMethod = :paymentMethod',
                { paymentMethod: PaymentMethod.CASH },
            )
            .andWhere(
                'consultationRequestEntity.paymentStatus = :paymentStatus',
                {
                    paymentStatus: PaymentStatus.PAID,
                },
            )
            .andWhere('consultationRequestEntity.isKashfFeePaid = false')
            .andWhere('consultationRequestEntity.updatedAt >= :date', {
                date: date,
            })
            .andWhere('doctor.isActive = true');

        const requests = await query.getMany();

        return requests;
    }

    async activatePendingShareDoctorAndSendPaymentUnsettledNotification(
        userId: number,
        doctorId: number,
        requestId: number,
        appLanguage: string,
    ) {
        await this.doctorsService.setPendingShareStatusForDoctor(
            doctorId,
            DEACTIVATION_REASON.UNPAID_KASHF_FEES,
        );

        const notification: CreateNotificationDto = {
            userId,
            data: {
                requestId: requestId.toString(),
                type: NotificationTyps.NOTIFY_DOCTOR_KASHF_PAYMENT_UNSETTLED,
            },
            notificationTranslations: [
                {
                    title: `طلب الدفع`,
                    body: `قم بالدفع لإعادة تفعيل الحساب`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: `Payment Request`,
                    body: `Pay Kashf to reactivate account`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification = await this.notificationsService.createNotification(
            notification,
        );
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                userId,
                appLanguage,
            )
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    async getDoctorsFromExpiredAndFinishedRequests(): Promise<number[]> {
        const doctors = await this.consultationRequestsRepository.createQueryBuilder()
            .select('DISTINCT doctor_id')
            .where('status = :status1 OR status = :status2', {
                status1: ConsultationRequestStatus.FINISHED,
                status2: ConsultationRequestStatus.EXPIRED,
            })
            .andWhere('type = :type', { type: ConsultationType.ONLINE_CONSULTATION })
            .getRawMany();

        const doctorArray = [];

        doctors.forEach(doctor => doctorArray.push(doctor.doctor_id));

        return doctorArray;
    }

    async findRequestsOneDayBefore(): Promise<number[]> {

        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set reminder_type = '${ReminderType.ONE_DAY_BEFORE}'
        where consultation_requests.id in (select m.id
                                           from (SELECT id,
                                                        reminder_type,
                                                        status,
                                                        CASE
                                                            WHEN consultation_requests.from is null
                                                                THEN (consultation_requests.date || ' ' || '00:00:00')::timestamp
               WHEN consultation_requests.from is not null
                  THEN (consultation_requests.date || ' ' || consultation_requests.from)::timestamp
               END AS my_date
                                                 FROM consultation_requests) as m
                                           where date_trunc('day', m.my_date) =
                                                 date_trunc('day', now() - INTERVAL '1 day'))
--             and m.reminder_type is null
          and m.status = '${ConsultationRequestStatus.ACCEPTED}' RETURNING id;
    `);

        this.logger.log(`schedule_one_day_reminder_requested=>${JSON.stringify(requestsIds)}`);
        return requestsIds;
    }

    async findRequestsTwoHoursBefore(): Promise<number[]> {

        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set reminder_type = '${ReminderType.TWO_HOURS_BEFORE}'
        where consultation_requests.id in (select m.id
                                           from (SELECT id,
                                                        reminder_type,
                                                        status,
                                                        CASE
                                                            WHEN consultation_requests.from is null
                                                                THEN (consultation_requests.date || ' ' || '00:00:00')::timestamp
                    WHEN consultation_requests.from is not null
                      THEN (consultation_requests.date || ' ' || consultation_requests.from)::timestamp
                  END AS my_date
                                                 FROM consultation_requests) as m
                                           where date_trunc('hour', m.my_date) =
                                                 date_trunc('hour', now() - INTERVAL '2 hours'))
--               and m.reminder_type = '${ReminderType.ONE_DAY_BEFORE}'
          and m.status = '${ConsultationRequestStatus.ACCEPTED}' RETURNING id;
    `);

        this.logger.log(`schedule_2_hours_reminder_requested=>${JSON.stringify(requestsIds)}`);
        return requestsIds;
    }

    async findRequestsFifteenMinutesBefore(): Promise<number[]> {

        const [requestsIds] = await this.consultationRequestsRepository.query(`
        update
            consultation_requests
        set reminder_type = '${ReminderType.TWO_HOURS_BEFORE}'
        where consultation_requests.id in (select m.id
                                           from (SELECT id,
                                                        reminder_type,
                                                        type,
                                                        status,
                                                        CASE
                                                            WHEN consultation_requests.from is null
                                                                THEN (consultation_requests.date || ' ' || '00:00:00')::timestamp
                    WHEN consultation_requests.from is not null
                      THEN (consultation_requests.date || ' ' || consultation_requests.from)::timestamp
                  END AS my_date
                                                 FROM consultation_requests) as m
                                           where date_trunc('minute', m.my_date) =
                                                 date_trunc('minute', now() - INTERVAL '15 minutes'))
--               and m.reminder_type = '${ReminderType.TWO_HOURS_BEFORE}'
          and m.type = '${ConsultationType.ONLINE_CONSULTATION}'
          and m.status = '${ConsultationRequestStatus.ACCEPTED}'
    `);

        this.logger.log(`schedule_15_minutes_reminder_requested=>${JSON.stringify(requestsIds)}`);
        return requestsIds;
    }

    async findByIds(ids: number[]): Promise<ConsultationRequestEntity[]> {
        return this.consultationRequestsRepository.findBy({
            id: In(ids),
        });
    }
}
