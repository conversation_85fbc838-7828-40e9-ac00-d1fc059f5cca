import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DataDownloadService } from '../../shared/services/data-download.service';
import { SharedModule } from '../../shared/shared.module';
import { AuthModule } from '../auth/auth.module';
import { UsersController } from './users.controller';
import { UsersCorn } from './users.corn';
import { UsersMapper } from './users.mapper';
import { UsersService } from './users.service';
import { UserEntity } from './entities/user.entity';
import { UserTranslation } from './entities/user-translation.entity';
import { UserFcmToken } from './entities/user-fcm-tokens.entity';

@Module({
    imports: [
        forwardRef(() => AuthModule),
        TypeOrmModule.forFeature([
            UserEntity,
            UserTranslation,
            UserFcmToken,
        ]),
        SharedModule,
        JwtModule.register({}),
    ],
    controllers: [UsersController],
    exports: [UsersService, UsersMapper],
    providers: [UsersService, UsersMapper, DataDownloadService, UsersCorn],
})
export class UsersModule {}
