import { Injectable } from '@nestjs/common';
import * as util from 'util';
import * as vm from 'vm';
import * as path from 'path';
import * as fs from 'fs';
import { UserEntity } from '../users/entities/user.entity';
import { DoctorEntity } from '../doctors/entities/doctor.entity';
import { Patient } from '../patients/entities/patient.entity';
import { DataSource } from 'typeorm';

export interface IReplResult {
    success: boolean;
    result?: any;
    error?: string;
    output?: string;
    type?: string;
}

@Injectable()
export class ReplService {
    private context: vm.Context;
    private replServer: any;
    private srcPath: string;

    constructor() {
        // Determine the src directory path
        this.srcPath = this.findSrcPath();
        this.initializeContext();
    }

    private findSrcPath(): string {
        // Try to find the src directory relative to current working directory
        const cwd = process.cwd();
        const possiblePaths = [
            path.join(cwd, 'src'),
            path.join(cwd, '../src'),
            path.join(__dirname, '../../../src'), // if service is in src/repl or similar
            path.join(__dirname, '../../src'), // if service is in src/services
            path.join(__dirname, '../src'), // if service is in src
        ];

        for (const possiblePath of possiblePaths) {
            if (fs.existsSync(possiblePath)) {
                return path.resolve(possiblePath);
            }
        }

        // Fallback to current working directory if src not found
        console.warn(
            'Could not find src directory, using current working directory',
        );
        return cwd;
    }

    private initializeContext(): void {
        try {
            // Create a custom require function that resolves paths relative to src
            const customRequire = (modulePath: string) => {
                if (modulePath.startsWith('.') || modulePath.startsWith('/')) {
                    // For relative or absolute paths, resolve relative to src
                    let resolvedPath = path.resolve(this.srcPath, modulePath);

                    // Handle TypeScript files - try different extensions
                    const extensions = ['', '.js', '.ts', '.json'];
                    let isModuleFound = false;

                    for (const ext of extensions) {
                        const pathWithExt = resolvedPath + ext;
                        if (fs.existsSync(pathWithExt)) {
                            resolvedPath = pathWithExt;
                            isModuleFound = true;
                            break;
                        }
                    }

                    // If still not found, try index files
                    if (!isModuleFound) {
                        const indexExtensions = ['/index.js', '/index.ts'];
                        for (const indexExt of indexExtensions) {
                            const indexPath = resolvedPath + indexExt;
                            if (fs.existsSync(indexPath)) {
                                resolvedPath = indexPath;
                                isModuleFound = true;
                                break;
                            }
                        }
                    }

                    if (!isModuleFound && !fs.existsSync(resolvedPath)) {
                        throw new Error(
                            `Module not found: ${modulePath} (looked for ${resolvedPath})`,
                        );
                    }

                    // eslint-disable-next-line @typescript-eslint/no-require-imports
                    return require(resolvedPath);
                } else {
                    // For node_modules, use normal require
                    // eslint-disable-next-line @typescript-eslint/no-require-imports
                    return require(modulePath);
                }
            };

            // Create a new context with global objects
            this.context = vm.createContext({
                console: {
                    log: (...args) => this.captureOutput('log', args),
                    error: (...args) => this.captureOutput('error', args),
                    warn: (...args) => this.captureOutput('warn', args),
                    info: (...args) => this.captureOutput('info', args),
                },
                require: customRequire,
                process: {
                    ...process,
                    cwd: () => this.srcPath, // Override cwd to return src path
                },
                Buffer,
                global,
                __dirname: this.srcPath, // Set __dirname to src path
                __filename: path.join(this.srcPath, 'repl-context.js'),
                setTimeout,
                setInterval,
                clearTimeout,
                clearInterval,
                setImmediate,
                clearImmediate,
                // Add path utilities for working with src-relative paths
                path: {
                    ...path,
                    resolve: (...segments) => {
                        // If no segments or first segment is relative, resolve from src
                        if (
                            segments.length === 0 ||
                            segments[0].startsWith('.')
                        ) {
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                            return path.resolve(this.srcPath, ...segments);
                        }
                        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                        return path.resolve(...segments);
                    },
                },
                fs: {
                    ...fs,
                    // Override readFile methods to work relative to src
                    readFileSync: (
                        filePath: string,
                        options?: { encoding?: null; flag?: string },
                    ) => {
                        const resolvedPath = this.resolvePathFromSrc(filePath);
                        return fs.readFileSync(resolvedPath, options);
                    },
                    readFile: (
                        filePath: string,
                        options?: { encoding?: null; flag?: string } | (() => void),
                        callback?: () => void,
                    ) => {
                        if (typeof options === 'function') {
                            callback = options;
                            options = undefined;
                        }
                        const resolvedPath = this.resolvePathFromSrc(filePath);
                        return fs.readFile(resolvedPath, options, callback);
                    },
                    existsSync: (filePath: string) => {
                        const resolvedPath = this.resolvePathFromSrc(filePath);
                        return fs.existsSync(resolvedPath);
                    },
                },
            });

            // Add helper function to resolve paths from src
            this.context.resolvePath = (relativePath: string) =>
                this.resolvePathFromSrc(relativePath);

            // Add function to show current working directory
            this.context.pwd = () => this.srcPath;

            // Add helper to require modules with better error handling
            this.context.requireModule = (modulePath: string) => {
                try {
                    return customRequire(modulePath);
                } catch (error) {
                    // Provide helpful error message with available alternatives
                    const resolvedPath = this.resolvePathFromSrc(modulePath);
                    const dir = path.dirname(resolvedPath);
                    const basename = path.basename(resolvedPath);

                    let suggestions = [];
                    if (fs.existsSync(dir)) {
                        const files = fs.readdirSync(dir);
                        suggestions = files
                            .filter((f) => f.includes(basename.split('.')[0]))
                            .slice(0, 3);
                    }

                    const suggestionText =
                        suggestions.length > 0
                            ? `\n\nDid you mean one of these?\n${suggestions.map((s) => `- ${s}`).join('\n')}`
                            : '';

                    throw new Error(`${error.message} ${suggestionText}`);
                }
            };

            // Add file system exploration utilities
            this.context.ls = (dirPath = '.') => {
                const fullPath = this.resolvePathFromSrc(dirPath);
                if (!fs.existsSync(fullPath)) {
                    return `Directory not found: ${fullPath}`;
                }
                if (!fs.statSync(fullPath).isDirectory()) {
                    return `Not a directory: ${fullPath}`;
                }
                const files = fs.readdirSync(fullPath);
                return files
                    .map((file) => {
                        const filePath = path.join(fullPath, file);
                        const stat = fs.statSync(filePath);
                        const type = stat.isDirectory() ? 'DIR' : 'FILE';
                        return `${type}: ${file}`;
                    })
                    .join('\n');
            };

            this.context.find = (pattern, dir = '.') => {
                const fullPath = this.resolvePathFromSrc(dir);
                if (!fs.existsSync(fullPath)) {
                    return `Directory not found: ${fullPath}`;
                }

                const results = [];
                const searchRecursive = (
                    currentPath: string,
                    relativePath = '',
                ) => {
                    try {
                        const files = fs.readdirSync(currentPath);
                        files.forEach((file) => {
                            const filePath = path.join(currentPath, file);
                            const relativeFilePath = path.join(
                                relativePath,
                                file,
                            );

                            if (file.includes(pattern)) {
                                const stat = fs.statSync(filePath);
                                const type = stat.isDirectory()
                                    ? 'DIR'
                                    : 'FILE';
                                results.push(`${type}: ${relativeFilePath}`);
                            }

                            if (
                                fs.statSync(filePath).isDirectory() &&
                                results.length < 20
                            ) {
                                searchRecursive(filePath, relativeFilePath);
                            }
                        });
                    } catch (e) {
                        // Skip directories we can't read
                    }
                };

                searchRecursive(fullPath);
                return results.length > 0
                    ? results.join('\n')
                    : `No files matching '${pattern}' found`;
            };

            this.context.tree = (dirPath = '.', maxDepth = 3) => {
                const fullPath = this.resolvePathFromSrc(dirPath);
                if (!fs.existsSync(fullPath)) {
                    return `Directory not found: ${fullPath}`;
                }

                const buildTree = (
                    currentPath: string,
                    prefix = '',
                    depth = 0,
                ) => {
                    if (depth > maxDepth) {
                        return [];
                    }

                    try {
                        const files = fs.readdirSync(currentPath);
                        const result = [];

                        files.forEach((file, index) => {
                            const filePath = path.join(currentPath, file);
                            const isLast = index === files.length - 1;
                            const currentPrefix =
                                prefix + (isLast ? '└── ' : '├── ');
                            const nextPrefix =
                                prefix + (isLast ? '    ' : '│   ');

                            const stat = fs.statSync(filePath);
                            if (stat.isDirectory()) {
                                result.push(`${currentPrefix}${file}/`);
                                if (depth < maxDepth) {
                                    result.push(
                                        ...buildTree(
                                            filePath,
                                            nextPrefix,
                                            depth + 1,
                                        ),
                                    );
                                }
                            } else {
                                result.push(`${currentPrefix}${file}`);
                            }
                        });

                        return result;
                    } catch (e) {
                        return [`${prefix}[Permission denied]`];
                    }
                };

                const tree = buildTree(fullPath);
                return `${dirPath}/\n` + tree.join('\n');
            };

            // Add debug function to test auto-return processing
            this.context.debugCode = (code: string) => {
                const processed = this.processCodeForAutoReturn(code);
                return {
                    original: code,
                    processed,
                    willAutoReturn: processed !== code,
                };
            };

            // Add NestJS app context dynamically (check at runtime)
            Object.defineProperty(this.context, 'app', {
                get: () => global.app,
                enumerable: true,
                configurable: true,
            });

            // Add common services and modules (simple dynamic functions)
            this.context.getService = (serviceClass: any) => {
                if (!global.app) {
                    throw new Error('NestJS app context not available');
                }
                try {
                    return global.app.get(serviceClass);
                } catch (error) {
                    throw new Error(
                        `Service not found: ${serviceClass.name || serviceClass}`,
                    );
                }
            };

            // Add TypeORM connection if available
            this.context.getConnection = () => {
                if (!global.app) {
                    throw new Error('NestJS app context not available');
                }
                try {
                    const { DataSource } = require('typeorm');
                    return global.app.get(DataSource);
                } catch (error) {
                    throw new Error('TypeORM DataSource not available');
                }
            };

            // Add entity manager
            this.context.getEntityManager = () => {
                try {
                    const connection = this.context.getConnection();
                    return connection.manager;
                } catch (error) {
                    throw new Error('EntityManager not available');
                }
            };

            // Add repository helper
            this.context.getRepository = (entity: any) => {
                try {
                    const connection = this.context.getConnection();
                    return connection.getRepository(entity);
                } catch (error) {
                    throw new Error(
                        `Repository for ${entity.name || entity} not found`,
                    );
                }
            };

            // Pre-load common entities for easy access
            this.context.UserEntity = UserEntity;
            this.context.DoctorEntity = DoctorEntity;
            this.context.Patient = Patient;

            this.context.moment = require('moment');

            // Add convenience methods for common repositories
            this.context.getUserRepository = () => {
                try {
                    const connection = this.context.getConnection();
                    return connection.getRepository(UserEntity);
                } catch (error) {
                    throw new Error('UserRepository not available');
                }
            };

            this.context.getDoctorRepository = () => {
                try {
                    const connection = this.context.getConnection();
                    return connection.getRepository(DoctorEntity);
                } catch (error) {
                    throw new Error('DoctorRepository not available');
                }
            };

            this.context.getPatientRepository = () => {
                try {
                    const connection = this.context.getConnection();
                    return connection.getRepository(Patient);
                } catch (error) {
                    throw new Error('PatientRepository not available');
                }
            };

            // Add utility functions
            this.context.inspect = util.inspect;
            this.context.help = () => {
                const hasApp = !!global.app;
                return `
Kashf REPL - Available Commands:

${hasApp ? '✅ NestJS Application Context Available' : '❌ NestJS Application Context Not Available'}
📁 Working Directory: ${this.srcPath}

Basic Functions:
- help(): Show this help message
- inspect(obj): Inspect an object with detailed formatting
- console.log/error/warn/info(): Output to console
- pwd(): Show current working directory (src path)
- resolvePath(path): Resolve a path relative to src directory
- requireModule(path): Require with better error messages and TypeScript support

File System Exploration:
- ls(dir): List files in directory (default: current directory)
- find(pattern, dir): Search for files/folders matching pattern
- tree(dir, depth): Show directory structure (default depth: 3)

Path Handling:
- All relative paths are resolved from the src directory
- require('./path/to/module') - loads from src/path/to/module (supports .ts files)
- requireModule('./path/to/module') - same as require but with better errors
- fs.readFileSync('./config.json') - reads from src/config.json
- Custom require function handles src-relative imports and TypeScript files

${
    hasApp
        ? `
NestJS Functions:
- app: NestJS application instance
- getService(ServiceClass): Get a service from the DI container
- getConnection(): Get TypeORM DataSource
- getEntityManager(): Get TypeORM EntityManager
- getRepository(EntityClass): Get repository for an entity

Pre-loaded Entities:
- UserEntity: User entity class
- DoctorEntity: Doctor entity class
- Patient: Patient entity class

Convenience Repository Methods:
- getUserRepository(): Get user repository
- getDoctorRepository(): Get doctor repository
- getPatientRepository(): Get patient repository

Example usage:
// Import a service from src directory
const myService = require('./services/my.service');

// Read a config file from src
const config = JSON.parse(fs.readFileSync('./config.json', 'utf8'));

// Get a service from DI container
const configService = getService('ConfigService');

// Database operations
const entityManager = getEntityManager();
const users = await entityManager.query('SELECT * FROM users LIMIT 5');

// Repository operations
const userRepo = getUserRepository();
const user = await userRepo.findOne({ where: { id: 10 } });
`
        : `
Limited Mode:
Only basic JavaScript functionality is available.
The main application context could not be loaded.
You can still use file operations relative to src directory.

Example:
// Read a file from src directory
const content = fs.readFileSync('./some-file.txt', 'utf8');
`
}

Tips:
- Use 'await' for async operations
- All paths are relative to src directory: ${this.srcPath}
- Use pwd() to see current working directory
- Results are automatically displayed
                `.trim();
            };
        } catch (error) {
            console.error('Failed to initialize REPL context:', error);
        }
    }

    private resolvePathFromSrc(filePath: string): string {
        if (path.isAbsolute(filePath)) {
            return filePath;
        }
        return path.resolve(this.srcPath, filePath);
    }

    private outputBuffer: string[] = [];

    private captureOutput(type: string, args: any[]): void {
        const message = args
            .map((arg) =>
                typeof arg === 'object'
                    ? util.inspect(arg, { depth: 2, colors: false })
                    : String(arg),
            )
            .join(' ');
        this.outputBuffer.push(`[${type.toUpperCase()}] ${message}`);
    }

    async executeCode(code: string): Promise<IReplResult> {
        this.outputBuffer = []; // Clear previous output

        try {
            // Validate code before execution
            if (!code || code.trim().length === 0) {
                return {
                    success: false,
                    error: 'No code provided',
                };
            }

            // Process the code to make the last expression returnable
            const processedCode = this.processCodeForAutoReturn(code);

            // Wrap the code in an async function to support await
            const wrappedCode = `
                (async () => {
                    try {
                        ${processedCode}
                    } catch (innerError) {
                        throw new Error('Execution error: ' + (innerError.message || String(innerError)));
                    }
                })()
            `;

            const result = await vm.runInContext(wrappedCode, this.context, {
                timeout: 15000, // 15 second timeout
                displayErrors: true,
                breakOnSigint: true,
                filename: path.join(this.srcPath, 'repl-execution.js'), // Set filename for better error traces
            });

            const output = this.outputBuffer.join('\n');

            return {
                success: true,
                result,
                output: output || undefined,
                type: typeof result,
            };
        } catch (error) {
            const output = this.outputBuffer.join('\n');

            // Enhanced error handling
            let errorMessage = 'Unknown error occurred';
            if (error instanceof Error) {
                errorMessage = error.message;
                if (error.stack) {
                    // Clean up the stack trace to be more readable
                    const cleanStack = error.stack
                        .split('\n')
                        .filter(
                            (line) =>
                                !line.includes('vm.js') &&
                                !line.includes('node_modules'),
                        )
                        .slice(0, 5)
                        .join('\n');
                    if (cleanStack) {
                        errorMessage += '\n' + cleanStack;
                    }
                }
            } else {
                errorMessage = String(error);
            }

            return {
                success: false,
                error: errorMessage,
                output: output || undefined,
            };
        }
    }

    private processCodeForAutoReturn(code: string): string {
        // Remove leading/trailing whitespace
        const trimmedCode = code.trim();

        // Split into statements by semicolon and newlines, but be smart about it
        const statements = this.parseStatements(trimmedCode);

        if (statements.length === 0) {
            return code;
        }

        // Get the last statement
        const lastStatement = statements[statements.length - 1].trim();

        // Check if the last statement should be auto-returned
        const shouldAutoReturn = this.shouldAutoReturnStatement(lastStatement);

        if (shouldAutoReturn) {
            // Replace the last statement with a return statement
            const previousStatements = statements.slice(0, -1);
            const modifiedCode =
                [...previousStatements, `return (${lastStatement})`].join(
                    ';\n',
                ) + ';';

            return modifiedCode;
        }

        return code;
    }

    private parseStatements(code: string): string[] {
        // Simple statement parsing - split by lines and semicolons
        // but keep track of context to avoid splitting inside strings/objects

        const lines = code.split('\n');
        const statements = [];
        let currentStatement = '';

        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine) {
                if (currentStatement) {
                    currentStatement += '\n' + trimmedLine;
                } else {
                    currentStatement = trimmedLine;
                }

                // If line ends with semicolon and we're not in a block, treat as complete statement
                if (
                    trimmedLine.endsWith(';') &&
                    !this.isInBlock(currentStatement)
                ) {
                    statements.push(currentStatement.replace(/;+$/, ''));
                    currentStatement = '';
                }
            }
        }

        // Add the last statement if there's one
        if (currentStatement.trim()) {
            statements.push(currentStatement.trim().replace(/;+$/, ''));
        }

        return statements.filter((s) => s.length > 0);
    }

    private isInBlock(code: string): boolean {
        // Simple check - count braces to see if we're in a block
        let braceCount = 0;
        let inString = false;
        let stringChar = '';

        for (let i = 0; i < code.length; i++) {
            const char = code[i];
            const prevChar = i > 0 ? code[i - 1] : '';

            if ((char === '"' || char === "'") && prevChar !== '\\') {
                if (!inString) {
                    inString = true;
                    stringChar = char;
                } else if (char === stringChar) {
                    inString = false;
                    stringChar = '';
                }
            }

            if (!inString) {
                if (char === '{') {
                    braceCount++;
                }
                if (char === '}') {
                    braceCount--;
                }
            }
        }

        return braceCount > 0;
    }

    private shouldAutoReturnStatement(statement: string): boolean {
        // Remove trailing semicolon for analysis
        const cleanStatement = statement.replace(/;+$/, '').trim();

        // Don't auto-return if statement is empty
        if (!cleanStatement) {
            return false;
        }

        // Don't auto-return statements that already have return
        if (cleanStatement.startsWith('return ')) {
            return false;
        }

        // Don't auto-return control flow statements
        const controlFlowPatterns = [
            /^if\s*\(/,
            /^else\s*{?/,
            /^for\s*\(/,
            /^while\s*\(/,
            /^do\s*{/,
            /^switch\s*\(/,
            /^try\s*{/,
            /^catch\s*\(/,
            /^finally\s*{/,
            /^throw\s+/,
            /^break\s*;?$/,
            /^continue\s*;?$/,
            /^function\s+/,
            /^class\s+/,
            /^import\s+/,
            /^export\s+/,
        ];

        for (const pattern of controlFlowPatterns) {
            if (pattern.test(cleanStatement)) {
                return false;
            }
        }

        // Don't auto-return if it looks like a block (ends with { or })
        if (cleanStatement.endsWith('{') || cleanStatement.endsWith('}')) {
            return false;
        }

        // Don't auto-return variable declarations without assignment
        if (/^(const|let|var)\s+\w+\s*$/.test(cleanStatement)) {
            return false;
        }

        // Don't auto-return certain side-effect operations
        const sideEffectPatterns = [/^console\./, /^process\.exit/];

        for (const pattern of sideEffectPatterns) {
            if (pattern.test(cleanStatement)) {
                return false;
            }
        }

        // Auto-return everything else:
        // - Variable assignments: const x = something
        // - Expressions: someObject.property
        // - Function calls: getRepository(...)
        // - Simple references: variableName
        return true;
    }

    getContextInfo() {
        try {
            const contextKeys = Object.keys(this.context).filter(
                (key) =>
                    ![
                        'console',
                        'require',
                        'process',
                        'Buffer',
                        'global',
                        '__dirname',
                        '__filename',
                    ].includes(key),
            );

            return {
                availableServices: global.app
                    ? 'NestJS app context available'
                    : 'No app context',
                contextKeys,
                helpAvailable: true,
                srcPath: this.srcPath,
                workingDirectory: this.srcPath,
            };
        } catch (error) {
            return {
                error: error.message,
            };
        }
    }

    // Method to get the current src path
    getSrcPath(): string {
        return this.srcPath;
    }

    // Method to manually set src path if needed
    setSrcPath(newPath: string): void {
        if (fs.existsSync(newPath)) {
            this.srcPath = path.resolve(newPath);
            this.initializeContext(); // Reinitialize context with new path
        } else {
            throw new Error(`Path does not exist: ${newPath}`);
        }
    }
}
