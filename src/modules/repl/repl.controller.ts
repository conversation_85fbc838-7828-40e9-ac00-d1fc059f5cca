import { Controller, Get, Post, Body } from '@nestjs/common';
import { ReplService } from './repl.service';

@Controller('api')
export class ReplController {
    constructor(private readonly replService: ReplService) {}

    @Get('context')
    async getContext() {
        return this.replService.getContextInfo();
    }

    @Post('execute')
    async executeCode(@Body() body: { code: string }) {
        return this.replService.executeCode(body.code);
    }
}
