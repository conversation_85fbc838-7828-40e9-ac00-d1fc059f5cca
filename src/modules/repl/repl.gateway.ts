import {
    WebSocketGateway,
    WebSocketServer,
    SubscribeMessage,
    MessageBody,
    ConnectedSocket,
    OnGatewayConnection,
    OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { ReplService } from './repl.service';

@WebSocketGateway({
    cors: {
        origin: '*',
    },
})
export class ReplGateway implements OnGatewayConnection, OnGatewayDisconnect {
    @WebSocketServer()
    server: Server;

    constructor(private readonly replService: ReplService) {}

    handleConnection(client: Socket) {
        console.log(`Client connected: ${client.id}`);
        
        // Send welcome message and context info
        this.replService.getContextInfo().then(info => {
            client.emit('context-info', info);
        });
    }

    handleDisconnect(client: Socket) {
        console.log(`Client disconnected: ${client.id}`);
    }

    @SubscribeMessage('execute-code')
    async handleExecuteCode(
        @MessageBody() data: { code: string },
        @ConnectedSocket() client: Socket,
    ) {
        try {
            const result = await this.replService.executeCode(data.code);
            client.emit('execution-result', result);
        } catch (error) {
            client.emit('execution-result', {
                success: false,
                error: error.message || 'Unknown error occurred',
            });
        }
    }

    @SubscribeMessage('get-context')
    async handleGetContext(@ConnectedSocket() client: Socket) {
        try {
            const info = await this.replService.getContextInfo();
            client.emit('context-info', info);
        } catch (error) {
            client.emit('context-info', {
                error: error.message || 'Failed to get context info',
            });
        }
    }
}
