import { Module } from '@nestjs/common';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { ReplController } from './repl.controller';
import { ReplService } from './repl.service';

@Module({
    imports: [
        ServeStaticModule.forRoot({
            rootPath: join(__dirname, '..', '..', '..', 'repl-gui'),
            serveRoot: '/',
        }),
    ],
    controllers: [ReplController],
    providers: [ReplService],
})
export class ReplModule {}
