# Kashf REPL - Layout Features

## Overview
The Kashf REPL now supports both vertical and horizontal layouts with resizable panels, providing a flexible development environment similar to modern IDEs.

## Layout Options

### Horizontal Layout (Default)
- Code editor on top
- Results panel on bottom
- Resizable with horizontal divider
- Good for wide screens and traditional coding workflow

### Vertical Layout
- Code editor on left
- Results panel on right
- Resizable with vertical divider
- Ideal for ultrawide monitors and side-by-side comparison
- **Toggleable sidebar** - Hide/show the context sidebar for maximum coding space
- **Action buttons remain in separate row** - Controls stay above the editor/output panels for easy access

## Features

### Layout Switching
- Toggle between layouts using the buttons in the header
- **Horizontal**: Stack panels vertically (editor above, results below)
- **Vertical**: Place panels side-by-side (editor left, results right)
- Layout preference is automatically saved to localStorage

### Resizable Panels
- Drag the divider between panels to resize
- Minimum panel sizes are enforced (300px width, 200px height)
- Smooth resizing with visual feedback
- CodeMirror editor automatically refreshes during and after resize

### Sidebar Management (Vertical Mode)
- **Toggle Button**: Click the hamburger menu (☰) in the top-left to hide/show sidebar
- **Auto-hide**: Sidebar can be hidden to maximize coding space in vertical mode
- **Persistent State**: Sidebar visibility preference is saved between sessions
- **Horizontal Mode**: Sidebar is always visible in horizontal mode

### Keyboard Shortcuts
All existing shortcuts remain functional:
- **Ctrl+Enter**: Execute code
- **Ctrl+/**: Toggle comment
- **Ctrl+L**: Clear results
- **Ctrl+K**: Clear editor
- And many more...

## Technical Implementation

### CSS Classes
- `.editor-container.horizontal`: Horizontal layout (default)
- `.editor-container.vertical`: Vertical layout
- `.resizer`: Draggable divider between panels
- `.resizer.horizontal`: Horizontal divider for vertical layout

### JavaScript Methods
- `setLayout(layout)`: Switch between 'horizontal' and 'vertical'
- `setupResizer()`: Initialize resizable functionality
- Automatic layout persistence via localStorage

### Responsive Design
- Panels maintain minimum sizes to ensure usability
- Editor automatically refreshes on layout changes
- Window resize events trigger editor refresh

## Usage Tips

1. **For Wide Screens**: Use vertical layout to maximize horizontal space
2. **For Standard Screens**: Use horizontal layout for better readability
3. **Drag to Resize**: Click and drag the divider to adjust panel sizes
4. **Persistent Layout**: Your layout choice is remembered between sessions

## Browser Compatibility
- Modern browsers with CSS Flexbox support
- Mouse events for resizing functionality
- localStorage for preference persistence
